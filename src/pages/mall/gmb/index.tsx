import { Picker, View } from "@tarojs/components";
import Style from "./index.module.scss";
import { useSetState, useUpdateEffect } from "ahooks";
import dayjs from "dayjs";
import CusApi from "@/services/CusApi";
import Taro, { useDidShow } from "@tarojs/taro";
import clsx from "clsx";

export default () => {
  const [state, setState] = useSetState({
    date: dayjs().format("YYYY-MM"),

    user: null as any,
    stat: null as any,
    list: [] as any[],
  });

  useDidShow(() => {
    fetchUser();
    fetchList();
    fetchStat();
  });

  useUpdateEffect(() => {
    fetchList();
    fetchStat();
  }, [state.date]);

  const fetchUser = async () => {
    const user = await CusApi.getUserInfo();
    setState({ user });
  };

  const fetchList = async () => {
    const start = dayjs(state.date)
      .startOf("month")
      .format("YYYY-MM-DD HH:mm:ss");
    const end = dayjs(state.date).endOf("month").format("YYYY-MM-DD HH:mm:ss");

    const res = await CusApi.getWalletList({
      source: 2,
      startCreateDate: start,
      endCreateDate: end,
      pageSize: 9999,
    });
    const list = res?.list || [];

    setState({ list });
  };

  const fetchStat = async () => {
    const start = dayjs(state.date)
      .startOf("month")
      .format("YYYY-MM-DD HH:mm:ss");
    const end = dayjs(state.date).endOf("month").format("YYYY-MM-DD HH:mm:ss");
    const res = await CusApi.getWalletStat({
      source: 2,
      startCreateDate: start,
      endCreateDate: end,
    });
    setState({ stat: res || {} });
  };

  return (
    <View className={Style.page}>
      {/* <View className={Style.head}>
        <View className={Style.tit}>M币余额</View>
        <View className={Style.num}>{state.user?.coin ?? "--"}</View>

        <View
          className="pos-absolute top-150 right-50 h-50 px-20 bg-#000/15 rounded-full text-(26/50 #fff)"
          onClick={() => {
            Taro.navigateTo({ url: `/pages/mall/gmbDraw/index` });
          }}
        >
          去提现&gt;
        </View>

        <View className="mt-5">
          <View className="text-(24 #fff)">可提现M币：</View>
          <View className="mt-10 text-(32 #fff) fw-bold">
            {state.user?.cashCoin ?? "--"}
          </View>
        </View>
      </View> */}

      <View className="pos-relative mx-30 mt-30 p-40 bg-gradient-to-br from-#fff to-#f8f9fa rounded-20 shadow-lg">
        {/* 详细规则按钮 */}
        <View
          className="pos-absolute top-20 right-20 px-16 py-8 bg-#f0f0f0 rounded-full text-(24 #666) flex items-center"
          onClick={() => {
            // 跳转到详细规则页面
          }}
        >
          详细规则
          <View className="ml-4 text-16">›</View>
        </View>

        {/* M币余额标题 */}
        <View className="text-(28 #333) mb-16">M币余额</View>

        {/* M币余额数值 */}
        <View className="text-(56 #ff6b35) fw-bold mb-40 font-mono">
          {state.user?.coin ?? "9999.22"}
        </View>

        {/* 可提现M币和提现按钮 */}
        <View className="flex items-center justify-between">
          <View className="flex-1">
            <View className="text-(24 #666) mb-8">可提现M币</View>
            <View className="text-(40 #333) fw-bold font-mono">
              {state.user?.cashCoin ?? "3333"}
            </View>
          </View>

          <View
            className="ml-30 px-32 py-16 bg-gradient-to-r from-#ff6b35 to-#f7931e rounded-full text-(28 #fff) fw-bold shadow-md active:scale-95 transition-transform"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/gmbDraw/index` });
            }}
          >
            提现
          </View>
        </View>

        {/* 装饰性元素 */}
        <View className="pos-absolute top-0 right-80 w-100 h-100 bg-gradient-to-br from-#ff6b35/10 to-transparent rounded-full"></View>
        <View className="pos-absolute bottom-0 left-60 w-60 h-60 bg-gradient-to-tr from-#f7931e/15 to-transparent rounded-full"></View>
      </View>

      <View className={Style.filter}>
        <Picker
          mode="date"
          fields="month"
          value={state.date}
          end={dayjs().format("YYYY-MM-DD")}
          onChange={(e) => {
            setState({ date: e.detail.value });
          }}
        >
          <View className={Style.date}>
            <View className={Style.txt}>
              {dayjs(state.date).format("YYYY年MM月")}
            </View>
            <View className={Style.arr}></View>
          </View>
        </Picker>
        <View className={Style.extra}>
          收益：{state.stat?.revenueCoin ?? "--"}M币 支出：
          {state.stat?.expenditureCoin ?? "--"}M币
        </View>
      </View>

      <View className={Style.list}>
        {state.list.map((item) => {
          return (
            <View className={Style.item} key={item.id}>
              <View className={Style.left}>
                <View className={Style.name}>{item.moneyTypeName}</View>
                <View className={Style.date}>
                  {dayjs(item.createDate).format("YYYY-MM-DD HH:mm:ss")}
                </View>
              </View>
              <View
                className={clsx(
                  Style.right,
                  item.incomeExpenseType == 1 && Style.em
                )}
              >
                {item.coin}
              </View>
            </View>
          );
        })}
      </View>
      {!state.list.length && <View className={Style.empty}>暂无数据...</View>}
    </View>
  );
};
